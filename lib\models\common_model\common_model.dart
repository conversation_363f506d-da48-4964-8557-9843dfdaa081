class CommonModel {
  final bool status;
  final String message;

  CommonModel({required this.status, required this.message});

  factory CommonModel.fromJson(Map<String, dynamic> json) {
    return CommonModel(status: json['status'], message: json['message']);
  }

  Map<String, dynamic> toJson() {
    return {'status': status, 'message': message};
  }
}

class AcceptLikeResponse {
  final bool status;
  final String message;
  final int? toMessageId;

  AcceptLikeResponse({
    required this.status,
    required this.message,
    this.toMessageId,
  });

  factory AcceptLikeResponse.fromJson(Map<String, dynamic> json) {
    return AcceptLikeResponse(
      status: json['status'] ?? false,
      message: json['message'] ?? '',
      toMessageId: json['to_message_id'],
    );
  }

  Map<String, dynamic> toJson() {
    return {'status': status, 'message': message, 'to_message_id': toMessageId};
  }
}
