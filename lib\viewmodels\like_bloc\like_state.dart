part of 'like_bloc.dart';

class LikeState extends Equatable {
  final int curentTebIndex;
  final List<MoveInUser> moveInData;
  final List<MoveOutUser> moveOutData;
  final bool isLoadData;
  final bool isLoadingMoveOut;
  final bool isLoadingMoveIn;
  final bool isAcceptingLike;

  const LikeState({
    this.curentTebIndex = 0,
    this.isLoadData = false,
    this.isLoadingMoveOut = false,
    this.isLoadingMoveIn = false,
    this.isAcceptingLike = false,
    this.moveInData = const [],
    this.moveOutData = const [],
  });

  @override
  List<Object> get props => [
    curentTebIndex,
    isLoadData,
    isLoadingMoveOut,
    isLoadingMoveIn,
    isAcceptingLike,
    moveInData,
    moveOutData,
  ];

  LikeState copyWith({
    final int? curentTebIndex,
    final bool? isLoadData,
    final bool? isLoadingMoveOut,
    final bool? isLoadingMoveIn,
    final bool? isAcceptingLike,
    final List<MoveInUser>? moveInData,
    final List<MoveOutUser>? moveOutData,
  }) {
    return LikeState(
      curentTebIndex: curentTebIndex ?? this.curentTebIndex,
      isLoadData: isLoadData ?? this.isLoadData,
      isLoadingMoveOut: isLoadingMoveOut ?? this.isLoadingMoveOut,
      isLoadingMoveIn: isLoadingMoveIn ?? this.isLoadingMoveIn,
      isAcceptingLike: isAcceptingLike ?? this.isAcceptingLike,
      moveInData: moveInData ?? this.moveInData,
      moveOutData: moveOutData ?? this.moveOutData,
    );
  }
}
