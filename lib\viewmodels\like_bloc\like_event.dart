part of 'like_bloc.dart';

abstract class LikeEvent extends Equatable {
  const LikeEvent();

  @override
  List<Object> get props => [];
}

class TabChangedEvent extends LikeEvent {
  final int newIndex;
  const TabChangedEvent(this.newIndex);
}

class LoadMoveOutData extends LikeEvent {}

class LoadMoveInData extends LikeEvent {}

class AcceptLikeEvent extends LikeEvent {
  final int profileId;
  final bool isAccept;

  const AcceptLikeEvent({required this.profileId, required this.isAccept});

  @override
  List<Object> get props => [profileId, isAccept];
}
